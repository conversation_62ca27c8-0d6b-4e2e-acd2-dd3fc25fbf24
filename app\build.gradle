plugins {
    id 'com.android.application'
}

android {
    namespace 'com.fongmi.android.tv'

    compileSdk 35
    flavorDimensions = ["mode", "abi"]

    defaultConfig {
        applicationId "com.fongmi.android.tv"
        minSdk 21
        //noinspection ExpiredTargetSdkVersion
        targetSdk 28
        versionCode 403
        versionName "4.0.3"
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas".toString(), "eventBusIndex": "com.fongmi.android.tv.event.EventIndex"]
            }
        }
    }

    productFlavors {
        leanback {
            dimension "mode"
        }
        mobile {
            dimension "mode"
        }
        arm64_v8a {
            dimension "abi"
            ndk { abiFilters "arm64-v8a" }
        }
        armeabi_v7a {
            dimension "abi"
            ndk { abiFilters "armeabi-v7a" }
        }
    }

    buildFeatures {
        buildConfig true
        viewBinding true
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    packagingOptions {
        resources {
            exclude 'META-INF/beans.xml'
            exclude 'META-INF/versions/9/OSGI-INF/MANIFEST.MF'
        }
    }

    android.applicationVariants.configureEach { variant ->
        variant.outputs.configureEach { output ->
            outputFileName = "${variant.productFlavors[0].name}-${variant.productFlavors[1].name}.apk"
        }
    }

    configurations.configureEach {
        resolutionStrategy {
            force 'com.squareup.okhttp3:okhttp:' + okhttpVersion
        }
    }

    lint {
        disable 'UnsafeOptInUsageError'
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.aar"])
    implementation project(':catvod')
    implementation project(':chaquo')
    implementation project(':quickjs')
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.media:media:1.7.0'
    implementation 'androidx.media3:media3-common:1.8.0'
    implementation 'androidx.media3:media3-container:1.8.0'
    implementation 'androidx.media3:media3-database:1.8.0'
    implementation 'androidx.media3:media3-datasource:1.8.0'
    implementation 'androidx.media3:media3-datasource-okhttp:1.8.0'
    implementation 'androidx.media3:media3-datasource-rtmp:1.8.0'
    implementation 'androidx.media3:media3-decoder:1.8.0'
    implementation 'androidx.media3:media3-effect:1.8.0'
    implementation 'androidx.media3:media3-exoplayer:1.8.0'
    implementation 'androidx.media3:media3-exoplayer-dash:1.8.0'
    implementation 'androidx.media3:media3-exoplayer-hls:1.8.0'
    implementation 'androidx.media3:media3-exoplayer-rtsp:1.8.0'
    implementation 'androidx.media3:media3-exoplayer-smoothstreaming:1.8.0'
    implementation 'androidx.media3:media3-extractor:1.8.0'
    implementation 'androidx.media3:media3-ui:1.8.0'
    implementation 'androidx.room:room-runtime:2.7.1'
    implementation 'cat.ereza:customactivityoncrash:2.4.0'
    implementation('com.github.anilbeesetti.nextlib:nextlib-media3ext:0.8.4') { exclude group: 'androidx.media3' }
    implementation 'com.github.bassaer:materialdesigncolors:1.0.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'com.github.bumptech.glide:annotations:4.16.0'
    implementation('com.github.bumptech.glide:avif-integration:4.16.0') { exclude group: 'org.aomedia.avif.android', module: 'avif' }
    implementation 'com.github.bumptech.glide:okhttp3-integration:4.16.0'
    implementation 'com.github.jahirfiquitiva:TextDrawable:1.0.3'
    implementation 'com.github.thegrizzlylabs:sardine-android:0.9'
    implementation 'com.github.teamnewpipe:NewPipeExtractor:v0.24.6'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'com.google.zxing:core:3.5.3'
    implementation 'com.guolindev.permissionx:permissionx:1.8.0'
    implementation 'com.hierynomus:smbj:0.14.0'
    implementation 'io.antmedia:rtmp-client:3.2.0'
    implementation 'javax.servlet:javax.servlet-api:3.1.0'
    implementation 'org.aomedia.avif.android:avif:1.1.1.14d8e3c4'
    implementation 'org.eclipse.jetty:jetty-client:8.1.21.v20160908'
    implementation('org.eclipse.jetty:jetty-server:8.1.21.v20160908') { exclude group: 'org.eclipse.jetty.orbit', module: 'javax.servlet' }
    implementation('org.eclipse.jetty:jetty-servlet:8.1.21.v20160908') { exclude group: 'org.eclipse.jetty.orbit', module: 'javax.servlet' }
    implementation 'org.fourthline.cling:cling-core:2.1.1'
    implementation 'org.fourthline.cling:cling-support:2.1.1'
    implementation 'org.greenrobot:eventbus:3.3.1'
    implementation 'org.nanohttpd:nanohttpd:2.3.1'
    implementation('org.simpleframework:simple-xml:2.7.1') { exclude group: 'stax', module: 'stax-api' exclude group: 'xpp3', module: 'xpp3' }
    leanbackImplementation 'androidx.leanback:leanback:1.2.0'
    leanbackImplementation 'com.github.JessYanCoding:AndroidAutoSize:1.2.1'
    mobileImplementation 'androidx.biometric:biometric:1.1.0'
    mobileImplementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    mobileImplementation 'com.google.android.flexbox:flexbox:3.0.0'
    mobileImplementation('com.journeyapps:zxing-android-embedded:4.3.0') { transitive = false }
    annotationProcessor 'androidx.room:room-compiler:2.7.1'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    annotationProcessor 'org.greenrobot:eventbus-annotation-processor:3.3.1'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs_nio:2.1.4'
}